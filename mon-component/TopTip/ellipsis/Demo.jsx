import React from 'react';
/* 请勿更改mon标识行，用于替换页面demo代码展示 */
/* mon:demo-show import { TopTip } from '@roo/roo-b-mobile' */
import { TopTip } from '@roo/roo-b-mobile';

// 生成离骚文本
const lisaoText = `
 帝高阳之苗裔兮，朕皇考曰伯庸。
  摄提贞于孟陬兮，惟庚寅吾以降。
  皇览揆余初度兮，肇锡余以嘉名：
  名余曰正则兮，字余曰灵均。
  纷吾既有此内美兮，又重之以修能；
  扈江离与辟芷兮，纫秋兰以为佩。
  汩余若将不及兮，恐年岁之不吾与。
  朝搴阰之木兰兮，夕揽洲之宿莽。
  日月忽其不淹兮，春与秋其代序。
  惟草木之零落兮，恐美人之迟暮。
  不抚壮而弃秽兮，何不改乎此度？
  乘骐骥以驰骋兮，来吾道夫先路！
  昔三后之纯粹兮，固众芳之所在。
  杂申椒与菌桂兮，岂惟纫夫蕙茝！
  彼尧舜之耿介兮，既遵道而得路。
  何桀纣之猖披兮，夫唯捷径以窘步。
  惟夫党人之偷乐兮，路幽昧以险隘。
  岂余身之惮殃兮，恐皇舆之败绩！
  忽奔走以先后兮，及前王之踵武。
  荃不察余之中情兮，反信谗而齌怒。
  余固知謇謇之为患兮，忍而不能舍也。
`
function Demo() {
    return (
        <div>
            <h4>5行展开收起</h4>
            <TopTip
                type="success"
                icon={null}
                closable={false}
                maxLine={10}
                expanded
                title={lisaoText.repeat(2)}
            />
            <div style={{ height: 10 }} />
            <h4>默认一行</h4>
            <TopTip
                type="warning"
                expanded
                title={lisaoText}
            />
            <div style={{ height: 10 }} />
            <h4>什么都不显示</h4>
            <TopTip
                type="danger"
                expanded
                maxLine={2}
                title={lisaoText}
            />
            <h4>按钮文案、不显示省略号、自定义展开回调</h4>
            <div style={{ height: 10 }} />
            <TopTip
                type="card"
                icon={null}
                closable
                maxLine={3}
                expanded={{
                    expandedText: '展开1',
                    collapsedText: '收起1',
                    ellipsis: false,
                    onExpanded: (visible) => {
                        console.log(visible, 'visible');
                    }
                }}
                title={lisaoText}
            />
            <div style={{ height: 10 }} />
            <h4>默认</h4>
            <div style={{
                background: "#FFFBE0"
            }}>
                <div>
                    <TopTip type="info" closable={false} icon={null}
                        style={{
                            padding: 0
                        }}
                        maxLine={null}
                        title={
                            <>
                                <span style={{ fontWeight: 'bold' }}>这是标题：</span> 我想是因为我想是因为我想是因为我想是因为我想是因为我想是因为我想是因为我想是因为我想是因为我想是因为
                            </>
                        } />
                    <TopTip type="info" closable={false} icon={null}
                        style={{
                            padding: 0
                        }}
                        expanded
                        maxLine={1}
                        title={
                            <>
                                <span style={{ fontWeight: 'bold' }}>这是标题一：</span> 这是标题描述这是标题描述这是标题描述这是标题描述这是标题描述这是标题描述这是标题描述这是标题描述这是标题描述
                            </>
                        } />
                </div>
            </div>

        </div>
    );
}
export default Demo;
